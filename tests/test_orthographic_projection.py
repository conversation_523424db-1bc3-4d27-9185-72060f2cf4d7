#!/usr/bin/env python3
"""
Test script for orthographic projection support in 3D Gaussian Splatting.

This script tests:
1. Basic orthographic projection rendering
2. Comparison with perspective projection
3. Backward compatibility (perspective projection still works)
"""

import torch
import numpy as np
import sys
import os

# Add the submodule to the path
sys.path.append('submodules/diff-gaussian-rasterization')

from diff_gaussian_rasterization import GaussianRasterizationSettings, GaussianRasterizer

def create_test_gaussians(num_gaussians=100):
    """Create a simple set of test Gaussians for rendering."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Create random 3D positions
    means3D = torch.randn(num_gaussians, 3, device=device) * 2.0
    
    # Create random scales (small)
    scales = torch.rand(num_gaussians, 3, device=device) * 0.1 + 0.01
    
    # Create random rotations (quaternions)
    rotations = torch.randn(num_gaussians, 4, device=device)
    rotations = rotations / torch.norm(rotations, dim=1, keepdim=True)
    
    # Create random opacities
    opacities = torch.rand(num_gaussians, 1, device=device)
    
    # Create random colors (RGB)
    colors = torch.rand(num_gaussians, 3, device=device)
    
    return means3D, scales, rotations, opacities, colors

def create_camera_matrices(use_orthographic=False, ortho_width=4.0, ortho_height=4.0):
    """Create camera view and projection matrices."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Simple view matrix (looking down the z-axis)
    view_matrix = torch.eye(4, device=device)
    view_matrix[2, 3] = -5.0  # Move camera back 5 units
    
    # Projection matrix
    if use_orthographic:
        # For orthographic projection, we'll use an identity-like projection
        # The actual orthographic projection is handled in the CUDA code
        proj_matrix = torch.eye(4, device=device)
    else:
        # Simple perspective projection
        fov = np.pi / 4  # 45 degrees
        aspect = 1.0
        near = 0.1
        far = 100.0
        
        f = 1.0 / np.tan(fov / 2)
        proj_matrix = torch.zeros(4, 4, device=device)
        proj_matrix[0, 0] = f / aspect
        proj_matrix[1, 1] = f
        proj_matrix[2, 2] = (far + near) / (near - far)
        proj_matrix[2, 3] = (2 * far * near) / (near - far)
        proj_matrix[3, 2] = -1
    
    return view_matrix, proj_matrix

def test_perspective_projection():
    """Test that perspective projection still works (backward compatibility)."""
    print("Testing perspective projection (backward compatibility)...")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Create test data
    means3D, scales, rotations, opacities, colors = create_test_gaussians(50)
    view_matrix, proj_matrix = create_camera_matrices(use_orthographic=False)
    
    # Camera parameters
    image_width, image_height = 512, 512
    fov = np.pi / 4
    tan_fovx = tan_fovy = np.tan(fov / 2)
    
    # Background color
    bg_color = torch.tensor([0.0, 0.0, 0.0], device=device)
    
    # Camera position
    campos = torch.tensor([0.0, 0.0, 5.0], device=device)
    
    # Create rasterization settings (perspective)
    raster_settings = GaussianRasterizationSettings(
        image_height=image_height,
        image_width=image_width,
        tanfovx=tan_fovx,
        tanfovy=tan_fovy,
        bg=bg_color,
        scale_modifier=1.0,
        viewmatrix=view_matrix,
        projmatrix=proj_matrix,
        sh_degree=0,
        campos=campos,
        prefiltered=False,
        debug=False,
        antialiasing=False,
        use_orthographic=False,  # Perspective projection
        ortho_width=1.0,
        ortho_height=1.0
    )
    
    # Create rasterizer
    rasterizer = GaussianRasterizer(raster_settings=raster_settings)
    
    # Dummy means2D (will be computed in preprocessing)
    means2D = torch.zeros(means3D.shape[0], 2, device=device)
    
    try:
        # Render
        rendered_image, radii, depth = rasterizer(
            means3D=means3D,
            means2D=means2D,
            shs=None,
            colors_precomp=colors,
            opacities=opacities,
            scales=scales,
            rotations=rotations,
            cov3D_precomp=None
        )
        
        print(f"✓ Perspective projection successful!")
        print(f"  Rendered image shape: {rendered_image.shape}")
        print(f"  Image min/max: {rendered_image.min():.4f}/{rendered_image.max():.4f}")
        return True
        
    except Exception as e:
        print(f"✗ Perspective projection failed: {e}")
        return False

def test_orthographic_projection():
    """Test orthographic projection rendering."""
    print("Testing orthographic projection...")
    
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Create test data
    means3D, scales, rotations, opacities, colors = create_test_gaussians(50)
    view_matrix, proj_matrix = create_camera_matrices(use_orthographic=True)
    
    # Camera parameters
    image_width, image_height = 512, 512
    ortho_width, ortho_height = 4.0, 4.0  # World space dimensions
    
    # For orthographic projection, we still need tan_fov values for clipping
    tan_fovx = tan_fovy = 1.0  # These are used for clipping in orthographic mode
    
    # Background color
    bg_color = torch.tensor([0.0, 0.0, 0.0], device=device)
    
    # Camera position
    campos = torch.tensor([0.0, 0.0, 5.0], device=device)
    
    # Create rasterization settings (orthographic)
    raster_settings = GaussianRasterizationSettings(
        image_height=image_height,
        image_width=image_width,
        tanfovx=tan_fovx,
        tanfovy=tan_fovy,
        bg=bg_color,
        scale_modifier=1.0,
        viewmatrix=view_matrix,
        projmatrix=proj_matrix,
        sh_degree=0,
        campos=campos,
        prefiltered=False,
        debug=False,
        antialiasing=False,
        use_orthographic=True,  # Orthographic projection
        ortho_width=ortho_width,
        ortho_height=ortho_height
    )
    
    # Create rasterizer
    rasterizer = GaussianRasterizer(raster_settings=raster_settings)
    
    # Dummy means2D (will be computed in preprocessing)
    means2D = torch.zeros(means3D.shape[0], 2, device=device)
    
    try:
        # Render
        rendered_image, radii, depth = rasterizer(
            means3D=means3D,
            means2D=means2D,
            shs=None,
            colors_precomp=colors,
            opacities=opacities,
            scales=scales,
            rotations=rotations,
            cov3D_precomp=None
        )
        
        print(f"✓ Orthographic projection successful!")
        print(f"  Rendered image shape: {rendered_image.shape}")
        print(f"  Image min/max: {rendered_image.min():.4f}/{rendered_image.max():.4f}")
        return True
        
    except Exception as e:
        print(f"✗ Orthographic projection failed: {e}")
        return False

def main():
    """Main test function."""
    print("=" * 60)
    print("Testing 3D Gaussian Splatting Orthographic Projection")
    print("=" * 60)
    
    if not torch.cuda.is_available():
        print("Warning: CUDA not available, tests may not work properly")
    
    # Test perspective projection (backward compatibility)
    perspective_ok = test_perspective_projection()
    print()
    
    # Test orthographic projection
    orthographic_ok = test_orthographic_projection()
    print()
    
    # Summary
    print("=" * 60)
    print("Test Results:")
    print(f"  Perspective projection: {'✓ PASS' if perspective_ok else '✗ FAIL'}")
    print(f"  Orthographic projection: {'✓ PASS' if orthographic_ok else '✗ FAIL'}")
    
    if perspective_ok and orthographic_ok:
        print("\n🎉 All tests passed! Orthographic projection is working correctly.")
        return 0
    else:
        print("\n❌ Some tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
