#!/usr/bin/env python3
"""
Example usage of orthographic projection in 3D Gaussian Splatting.

This example shows how to use the extended GaussianRasterizationSettings
to render with orthographic projection instead of perspective projection.
"""

import torch
import numpy as np
import sys
import os

# Add the submodule to the path
sys.path.append('submodules/diff-gaussian-rasterization')

from diff_gaussian_rasterization import GaussianRasterizationSettings, GaussianRasterizer

def create_simple_scene():
    """Create a simple scene with a few Gaussians."""
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")
    
    # Create a simple 3x3 grid of Gaussians
    positions = []
    colors = []
    
    for x in [-1, 0, 1]:
        for y in [-1, 0, 1]:
            positions.append([x, y, 0])
            # Different colors for each Gaussian
            colors.append([abs(x), abs(y), 0.5])
    
    means3D = torch.tensor(positions, device=device, dtype=torch.float32)
    colors_precomp = torch.tensor(colors, device=device, dtype=torch.float32)
    
    # Small, uniform scales
    scales = torch.ones(9, 3, device=device) * 0.2
    
    # Identity rotations (no rotation)
    rotations = torch.tensor([[1, 0, 0, 0]] * 9, device=device, dtype=torch.float32)
    
    # Full opacity
    opacities = torch.ones(9, 1, device=device)
    
    return means3D, scales, rotations, opacities, colors_precomp

def render_perspective(means3D, scales, rotations, opacities, colors):
    """Render with perspective projection (original 3DGS)."""
    device = means3D.device
    
    # Camera setup for perspective projection
    view_matrix = torch.eye(4, device=device)
    view_matrix[2, 3] = -5.0  # Move camera back
    
    # Perspective projection matrix
    fov = np.pi / 4  # 45 degrees
    aspect = 1.0
    near = 0.1
    far = 100.0
    
    f = 1.0 / np.tan(fov / 2)
    proj_matrix = torch.zeros(4, 4, device=device)
    proj_matrix[0, 0] = f / aspect
    proj_matrix[1, 1] = f
    proj_matrix[2, 2] = (far + near) / (near - far)
    proj_matrix[2, 3] = (2 * far * near) / (near - far)
    proj_matrix[3, 2] = -1
    
    # Rasterization settings for perspective projection
    raster_settings = GaussianRasterizationSettings(
        image_height=512,
        image_width=512,
        tanfovx=np.tan(fov / 2),
        tanfovy=np.tan(fov / 2),
        bg=torch.tensor([0.0, 0.0, 0.0], device=device),
        scale_modifier=1.0,
        viewmatrix=view_matrix,
        projmatrix=proj_matrix,
        sh_degree=0,
        campos=torch.tensor([0.0, 0.0, 5.0], device=device),
        prefiltered=False,
        debug=False,
        antialiasing=False,
        use_orthographic=False,  # Perspective projection
        ortho_width=1.0,
        ortho_height=1.0
    )
    
    # Render
    rasterizer = GaussianRasterizer(raster_settings=raster_settings)
    means2D = torch.zeros(means3D.shape[0], 2, device=device)
    
    rendered_image, radii, depth = rasterizer(
        means3D=means3D,
        means2D=means2D,
        shs=None,
        colors_precomp=colors,
        opacities=opacities,
        scales=scales,
        rotations=rotations,
        cov3D_precomp=None
    )
    
    return rendered_image

def render_orthographic(means3D, scales, rotations, opacities, colors, ortho_width=4.0, ortho_height=4.0):
    """Render with orthographic projection (new feature)."""
    device = means3D.device
    
    # Camera setup for orthographic projection
    view_matrix = torch.eye(4, device=device)
    view_matrix[2, 3] = -5.0  # Move camera back
    
    # For orthographic projection, we use an identity-like projection matrix
    proj_matrix = torch.eye(4, device=device)
    
    # Rasterization settings for orthographic projection
    raster_settings = GaussianRasterizationSettings(
        image_height=512,
        image_width=512,
        tanfovx=1.0,  # Used for clipping in orthographic mode
        tanfovy=1.0,  # Used for clipping in orthographic mode
        bg=torch.tensor([0.0, 0.0, 0.0], device=device),
        scale_modifier=1.0,
        viewmatrix=view_matrix,
        projmatrix=proj_matrix,
        sh_degree=0,
        campos=torch.tensor([0.0, 0.0, 5.0], device=device),
        prefiltered=False,
        debug=False,
        antialiasing=False,
        use_orthographic=True,  # Orthographic projection
        ortho_width=ortho_width,   # World space width
        ortho_height=ortho_height  # World space height
    )
    
    # Render
    rasterizer = GaussianRasterizer(raster_settings=raster_settings)
    means2D = torch.zeros(means3D.shape[0], 2, device=device)
    
    rendered_image, radii, depth = rasterizer(
        means3D=means3D,
        means2D=means2D,
        shs=None,
        colors_precomp=colors,
        opacities=opacities,
        scales=scales,
        rotations=rotations,
        cov3D_precomp=None
    )
    
    return rendered_image

def main():
    """Main example function."""
    print("3D Gaussian Splatting Orthographic Projection Example")
    print("=" * 55)
    
    if not torch.cuda.is_available():
        print("Warning: CUDA not available, using CPU (may be slow)")
    
    # Create a simple scene
    print("Creating simple scene...")
    means3D, scales, rotations, opacities, colors = create_simple_scene()
    print(f"Created scene with {means3D.shape[0]} Gaussians")
    
    # Render with perspective projection
    print("\nRendering with perspective projection...")
    perspective_image = render_perspective(means3D, scales, rotations, opacities, colors)
    print(f"Perspective image shape: {perspective_image.shape}")
    print(f"Perspective image range: [{perspective_image.min():.4f}, {perspective_image.max():.4f}]")
    
    # Render with orthographic projection
    print("\nRendering with orthographic projection...")
    orthographic_image = render_orthographic(means3D, scales, rotations, opacities, colors, 
                                           ortho_width=4.0, ortho_height=4.0)
    print(f"Orthographic image shape: {orthographic_image.shape}")
    print(f"Orthographic image range: [{orthographic_image.min():.4f}, {orthographic_image.max():.4f}]")
    
    # Compare the two
    print("\nComparison:")
    print(f"Perspective projection uses perspective division (objects get smaller with distance)")
    print(f"Orthographic projection maintains object size regardless of distance")
    print(f"Both images have the same resolution but different projection characteristics")
    
    print("\n✓ Example completed successfully!")
    print("\nTo use orthographic projection in your own code:")
    print("1. Set use_orthographic=True in GaussianRasterizationSettings")
    print("2. Specify ortho_width and ortho_height (world space dimensions)")
    print("3. The tanfovx and tanfovy parameters are still used for clipping")

if __name__ == "__main__":
    main()
