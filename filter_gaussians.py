import torch
from scene.gaussian_model import GaussianModel_3dgs as GaussianModel
import argparse

def filter_gaussians(input_ply, output_ply, opacity_threshold=0.2):
    """
    Filter Gaussian points based on opacity threshold and save to new ply file
    
    Args:
        input_ply: Input ply file path
        output_ply: Output ply file path 
        opacity_threshold: Minimum opacity threshold (default: 0.2)
    """
    # Initialize model
    model = GaussianModel(sh_degree=3)
    
    # Load ply file
    model.load_ply(input_ply)
    
    # Get opacity mask
    opacity = model.get_opacity
    mask = opacity.squeeze() > opacity_threshold
    
    # Filter model parameters directly
    model._xyz = torch.nn.Parameter(model._xyz[mask])
    model._features_dc = torch.nn.Parameter(model._features_dc[mask])
    model._features_rest = torch.nn.Parameter(model._features_rest[mask])
    model._opacity = torch.nn.Parameter(model._opacity[mask])
    model._scaling = torch.nn.Parameter(model._scaling[mask])
    model._rotation = torch.nn.Parameter(model._rotation[mask])
    
    # Print statistics
    print(f"Original points: {len(opacity)}")
    print(f"Points after filtering: {mask.sum().item()}")
    print(f"Removed points: {len(opacity) - mask.sum().item()}")
    
    # Save filtered points
    model.save_ply(output_ply)
    print(f"Saved filtered point cloud to: {output_ply}")

if __name__ == "__main__":
    input_file = 'output/truck_depth/point_cloud/iteration_30000/point_cloud.ply'
    output_file = 'output/truck_depth/point_cloud/iteration_30000/point_cloud_filter.ply'
    
    filter_gaussians(input_file, output_file, 0.2)