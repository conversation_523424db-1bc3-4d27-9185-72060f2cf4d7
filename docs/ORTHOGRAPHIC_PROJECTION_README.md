# 3D Gaussian Splatting Orthographic Projection Extension

This extension adds orthographic projection support to the 3D Gaussian Splatting CUDA rendering pipeline, allowing you to render with orthographic projection while maintaining full backward compatibility with the existing perspective projection.

## Features

- ✅ **Orthographic projection rendering** - Generate orthographic views of your 3D Gaussian scenes
- ✅ **Backward compatibility** - Existing perspective projection code continues to work unchanged
- ✅ **Unified interface** - Switch between projection modes with a simple parameter
- ✅ **Training compatibility** - Train with perspective projection, render with orthographic projection
- ✅ **CUDA optimized** - Full GPU acceleration for both projection modes

## Key Changes

### Python Interface

The `GaussianRasterizationSettings` class now includes three new parameters:

```python
GaussianRasterizationSettings(
    # ... existing parameters ...
    use_orthographic=False,    # Enable orthographic projection
    ortho_width=1.0,          # World space width for orthographic projection
    ortho_height=1.0          # World space height for orthographic projection
)
```

### CUDA Implementation

- Added `computeCov2D_ortho()` function for orthographic covariance computation
- Modified preprocessing kernels to conditionally use orthographic projection
- Updated focal length calculation for orthographic mode
- Maintained all existing perspective projection functionality

## Usage

### Basic Orthographic Rendering

```python
from diff_gaussian_rasterization import GaussianRasterizationSettings, GaussianRasterizer

# Create rasterization settings for orthographic projection
raster_settings = GaussianRasterizationSettings(
    image_height=512,
    image_width=512,
    tanfovx=1.0,              # Used for clipping
    tanfovy=1.0,              # Used for clipping
    bg=background_color,
    scale_modifier=1.0,
    viewmatrix=view_matrix,
    projmatrix=proj_matrix,
    sh_degree=0,
    campos=camera_position,
    prefiltered=False,
    debug=False,
    antialiasing=False,
    use_orthographic=True,    # Enable orthographic projection
    ortho_width=4.0,         # 4 units wide in world space
    ortho_height=4.0         # 4 units tall in world space
)

# Render as usual
rasterizer = GaussianRasterizer(raster_settings=raster_settings)
rendered_image, radii, depth = rasterizer(
    means3D=means3D,
    means2D=means2D,
    shs=shs,
    colors_precomp=None,
    opacities=opacities,
    scales=scales,
    rotations=rotations,
    cov3D_precomp=None
)
```

### Perspective Projection (Unchanged)

```python
# Existing perspective projection code works exactly as before
raster_settings = GaussianRasterizationSettings(
    image_height=512,
    image_width=512,
    tanfovx=tan_fovx,
    tanfovy=tan_fovy,
    bg=background_color,
    scale_modifier=1.0,
    viewmatrix=view_matrix,
    projmatrix=proj_matrix,
    sh_degree=0,
    campos=camera_position,
    prefiltered=False,
    debug=False,
    antialiasing=False
    # use_orthographic defaults to False
    # ortho_width and ortho_height have default values
)
```

## Parameters

### `use_orthographic` (bool, default: False)
- `False`: Use perspective projection (original behavior)
- `True`: Use orthographic projection

### `ortho_width` (float, default: 1.0)
- World space width of the orthographic viewing volume
- Determines how much of the scene is visible horizontally
- Larger values show more of the scene

### `ortho_height` (float, default: 1.0)
- World space height of the orthographic viewing volume
- Determines how much of the scene is visible vertically
- Larger values show more of the scene

## Training Workflow

The recommended workflow is to train with perspective projection and render with orthographic projection:

1. **Training**: Use normal 3DGS training with perspective projection
2. **Rendering**: Switch to orthographic projection for final rendering

```python
# During training - use perspective projection
train_settings = GaussianRasterizationSettings(
    # ... standard perspective settings ...
    use_orthographic=False
)

# During rendering - use orthographic projection
render_settings = GaussianRasterizationSettings(
    # ... same settings but with orthographic projection ...
    use_orthographic=True,
    ortho_width=desired_width,
    ortho_height=desired_height
)
```

## Technical Details

### Orthographic Projection Mathematics

In orthographic projection:
- No perspective division (objects don't get smaller with distance)
- Parallel projection lines
- Simplified Jacobian matrix for covariance computation

The key difference is in the Jacobian matrix used for 2D covariance computation:

**Perspective**: `J = [[fx/z, 0, -fx*x/z²], [0, fy/z, -fy*y/z²], [0, 0, 0]]`

**Orthographic**: `J = [[ratio_w, 0, 0], [0, ratio_h, 0], [0, 0, 0]]`

Where `ratio_w = image_width / ortho_width` and `ratio_h = image_height / ortho_height`.

### Backward Compatibility

- All existing code continues to work without modification
- Default parameter values maintain original behavior
- No performance impact on perspective projection

## Files Modified

- `diff_gaussian_rasterization/__init__.py` - Extended Python interface
- `cuda_rasterizer/forward.cu` - Added orthographic covariance computation
- `cuda_rasterizer/forward.h` - Updated function signatures
- `cuda_rasterizer/rasterizer.h` - Extended rasterizer interface
- `cuda_rasterizer/rasterizer_impl.cu` - Added orthographic focal length calculation
- `rasterize_points.h` - Updated C++ interface
- `rasterize_points.cu` - Extended parameter passing

## Testing

Run the included test scripts to verify the implementation:

```bash
# Test both projection modes
python test_orthographic_projection.py

# Run the example
python orthographic_projection_example.py
```

## Performance

- Orthographic projection has similar performance to perspective projection
- No additional memory overhead
- CUDA kernels are optimized for both projection modes

## Limitations

- Backward propagation (gradients) only supports perspective projection
- This is by design - train with perspective, render with orthographic
- Orthographic projection is intended for rendering/inference only

## Future Enhancements

Potential future improvements:
- Orthographic projection backward pass support
- Additional orthographic projection parameters
- Optimized orthographic-specific kernels

---

**Note**: This implementation maintains full backward compatibility. Existing 3DGS code will continue to work exactly as before without any modifications.
