#
# Copyright (C) 2023, Inria
# GRAPHDECO research group, https://team.inria.fr/graphdeco
# All rights reserved.
#
# This software is free for non-commercial, research and evaluation use 
# under the terms of the LICENSE.md file.
#
# For inquiries contact  george.<PERSON><PERSON><PERSON>@inria.fr
#

import pickle
import torch
import os
from os import makedirs
from scene1 import render_3dgs as render, GaussianModel_3dgs as GaussianModel
import torchvision
import cv2
import numpy as np
from utils.graphics_utils import getWorld2View2, getProjectionMatrix
import math
import json
from utils.graphics_utils import focal2fov
import open3d as o3d
import laspy

try:
    from diff_gaussian_rasterization import SparseGaussianAdam
    SPARSE_ADAM_AVAILABLE = True
except:
    SPARSE_ADAM_AVAILABLE = False

def save_depth_image(depth, save_file):
    depth_min = depth.min()
    depth_max = depth.max()
    depth_norm = (depth - depth_min) / (depth_max - depth_min)
    depth_uint8 = (depth_norm * 255).astype('uint8')
    depth_color = cv2.applyColorMap(depth_uint8, cv2.COLORMAP_JET)
    cv2.imwrite(save_file, depth_color)



def calculate_fov(output_width, output_height, f_x, f_y):
    fovx = focal2fov(f_x, output_width)
    fovy = focal2fov(f_y, output_height)
    return fovx, fovy


class DummyCamera:
    def __init__(self,img_name, R, T, FoVx, FoVy, f_x, f_y, w, h, znear=0.01, zfar=100.0):
        """
        R: cam 2 world
        T: world 2 cam
        """
        self.projection_matrix = getProjectionMatrix(znear=znear, zfar=zfar, fovX=FoVx, fovY=FoVy).transpose(0,1).cuda()
        self.R = R
        self.t = T
        self.world_view_transform = torch.tensor(getWorld2View2(R, T, np.array([0,0,0]), 1.0)).transpose(0, 1).cuda()
        self.full_proj_transform = (self.world_view_transform.unsqueeze(0).bmm(self.projection_matrix.unsqueeze(0))).squeeze(0)
        self.camera_center = self.world_view_transform.inverse()[3, :3]
        self.image_width = w
        self.image_height = h
        self.FoVx = FoVx
        self.FoVy = FoVy
        self.f_x = f_x
        self.f_y = f_y
        self.k = np.array([[f_x, 0, w/2], [0, f_y, h/2], [w/2, h/2, 1]])
        self.cam_name = img_name
    
    def translate(self, t):
        """
        t: translation vector (in camera coordinate)
        """
        if isinstance(t, list):
            t = np.array(t)
        cam_pos = -self.R @ self.t + t
        t_w2c = -self.R.T @ cam_pos
        self.t = t_w2c
        self.world_view_transform = torch.tensor(getWorld2View2(self.R, self.t, np.array([0,0,0]), 1.0)).transpose(0, 1).cuda()
        self.full_proj_transform = (self.world_view_transform.unsqueeze(0).bmm(self.projection_matrix.unsqueeze(0))).squeeze(0)
        self.camera_center = self.world_view_transform.inverse()[3, :3]

    def rotate(self, angles_list):
        """
        angles_list: [x, y, z] (in camera coordinate)
        """
        def get_rotation_matrix(angles):
            """
            Convert angles in degrees to rotation matrix
            angles: [x, y, z] angles in degrees
            pi: math.pi
            """
            # Convert degrees to radians
            rx, ry, rz = [angle * np.pi / 180.0 for angle in angles]
            
            # Rotation matrices around x, y, z axes
            Rx = np.array([[1, 0, 0],
                        [0, np.cos(rx), -np.sin(rx)],
                        [0, np.sin(rx), np.cos(rx)]])
            
            Ry = np.array([[np.cos(ry), 0, np.sin(ry)],
                        [0, 1, 0], 
                        [-np.sin(ry), 0, np.cos(ry)]])
            
            Rz = np.array([[np.cos(rz), -np.sin(rz), 0],
                        [np.sin(rz), np.cos(rz), 0],
                        [0, 0, 1]])
            
            # Combined rotation matrix
            R = Rx @ Ry @ Rz
            return R
        assert len(angles_list) == 3
        self.R = self.R @ get_rotation_matrix(angles_list)
        self.world_view_transform = torch.tensor(getWorld2View2(self.R, self.t, np.array([0,0,0]), 1.0)).transpose(0, 1).cuda()
        self.full_proj_transform = (self.world_view_transform.unsqueeze(0).bmm(self.projection_matrix.unsqueeze(0))).squeeze(0)
        self.camera_center = self.world_view_transform.inverse()[3, :3]

    def __str__(self):
        return f"DummyCamera(R={self.R}, T={self.t}, FoVx={self.FoVx}, FoVy={self.FoVy}, \
            image_width={self.image_width}, image_height={self.image_height}, world_view_transform={self.world_view_transform}, \
                projection_matrix={self.projection_matrix}, full_proj_transform={self.full_proj_transform}, camera_center={self.camera_center})"

def construct_camera_list(cameras_json_file, baseline = 0.5):
    with open(cameras_json_file, "r") as f:
        cam_info = json.load(f)
    stereo_pair_list = []
    # cam_idx = 170
    cam_idx = 0
    # _id = cam_info[cam_idx]["id"]
    R1 = np.array(cam_info[cam_idx]["rotation"]) # cam2world
    t1 = np.array(cam_info[cam_idx]["position"]) # cam2world
    t1 = -R1.T @ t1
    
    w_ori, h_ori = cam_info[cam_idx]["width"], cam_info[cam_idx]["height"]
    # w, h = 1600, 1066
    w, h = w_ori, h_ori
    scale = w_ori / w
    f_x = cam_info[cam_idx]["fx"] / scale
    f_y = cam_info[cam_idx]["fy"] / scale
    fovx, fovy = calculate_fov(w, h, f_x, f_y)
    cam_left = DummyCamera(f'left{cam_idx}',R1, t1, fovx, fovy, f_x, f_y, w, h, znear=1, zfar=100.0)
    t2 = t1 + np.array([-baseline, 0, 0])
    cam_right = DummyCamera(f'right{cam_idx}',R1, t2, fovx, fovy, f_x, f_y, w, h, znear=1, zfar=100.0)
    stereo_pair_list.append((cam_left, cam_right, baseline))
    return stereo_pair_list

def remap_left_view(left_img, right_img, left_depth, baseline, focal_length):
    height, width = left_img.shape[:2]

    x_coords = np.arange(width)
    y_coords = np.arange(height)
    xx, yy = np.meshgrid(x_coords, y_coords)
    
    disparity = baseline * focal_length / left_depth
    x_right = xx - disparity
    
    # Fill in the mapping coordinates
    map_x = (xx+disparity).astype(np.float32)
    map_y = yy.astype(np.float32)
    
        
    # Remap left image to right view perspective
    # dst(x,y) =  src(map_x(x,y),map_y(x,y))

    left_img_warped = cv2.remap(left_img, map_x, map_y, interpolation=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT)
    
    # Save warped image for visualization
    vis_warped = np.concatenate([right_img, left_img_warped], axis=0)
    return vis_warped

def verify_depth_consistency(left_img, right_img, left_depth, right_depth, baseline, focal_length, save_folder):
    height, width = left_img.shape[:2]

    x_coords = np.arange(width)
    y_coords = np.arange(height)
    xx, yy = np.meshgrid(x_coords, y_coords)
    
    disparity = baseline * focal_length / left_depth
    x_right = xx - disparity
    
    # Fill in the mapping coordinates
    map_x = (xx+disparity).astype(np.float32)
    map_y = yy.astype(np.float32)
    
        
    # Remap left image to right view perspective
    # dst(x,y) =  src(map_x(x,y),map_y(x,y))

    left_img_warped = cv2.remap(left_img, map_x, map_y, interpolation=cv2.INTER_LINEAR, borderMode=cv2.BORDER_CONSTANT)
    
    # Save warped image for visualization
    vis_warped = np.concatenate([right_img, left_img_warped], axis=0)
    cv2.imwrite(os.path.join(save_folder, "left_warped_to_right.png"), vis_warped[...,::-1])

    valid_mask = (x_right >= 0) & (x_right < width)
    x_right_int = np.round(x_right).astype(int)

    right_depth_warped = np.zeros_like(left_depth)
    right_depth_warped[valid_mask] = right_depth[yy[valid_mask], x_right_int[valid_mask]]
    
    depth_diff = np.abs(left_depth - right_depth_warped)
    
    relative_diff = depth_diff / left_depth 
    relative_diff[~valid_mask] = 0
    relative_diff_normalized = (relative_diff * 255).astype(np.uint8)
    relative_diff_color = cv2.applyColorMap(relative_diff_normalized, cv2.COLORMAP_JET)
    cv2.imwrite(os.path.join(save_folder, "relative_diff_color.png"), relative_diff_color)
    
    # threshold = 0.05
    # consistency_mask = (relative_diff < threshold) & valid_mask
    # consistency_mask_vis = (consistency_mask*255).astype(np.uint8)
    # consistency_mask_vis = cv2.applyColorMap(consistency_mask_vis, cv2.COLORMAP_JET)
    
    # cv2.imwrite(os.path.join(save_folder, "consistency_mask_vis.png"), consistency_mask_vis)



def render_depth_from_GS(pruned_gs_las, view_pair: list[DummyCamera]):
    """
    抽取gs中心点作为点云, 渲染深度图
    """
    
    las = laspy.read(pruned_gs_las)
    pcd = np.vstack((las.x, las.y, las.z)).transpose()
    left_cam = view_pair[0]
    k = left_cam.k
    R = left_cam.R.T # world 2 cam
    T = left_cam.T # world 2 cam
    # project pcd to camera coords
    pcd_cam = pcd @ R.T + T
    pcd_img = pcd_cam @ k.T
    pcd_img = pcd_img[:, :2] / pcd_cam[:, 2:3]

    # Get image dimensions from camera intrinsics
    height, width = left_cam.image_height, left_cam.image_width
    
    # Initialize depth map
    depth_map = np.zeros((height, width))
    
    # Convert to integer pixel coordinates and get depths
    pcd_img_int = np.round(pcd_img).astype(int)
    depths = pcd_cam[:, 2]
    
    # Filter points within image bounds
    valid_idx = (pcd_img_int[:,0] >= 0) & (pcd_img_int[:,0] < width) & \
                (pcd_img_int[:,1] >= 0) & (pcd_img_int[:,1] < height)
    
    pcd_img_valid = pcd_img_int[valid_idx]
    depths_valid = depths[valid_idx]
    
    # For each valid point, update depth map with closest depth
    for i in range(len(pcd_img_valid)):
        x, y = pcd_img_valid[i]
        depth = depths_valid[i]
        if depth < 0: continue
        if depth_map[y,x] == 0 or depth < depth_map[y,x]:
            depth_map[y,x] = depth
            
    return depth_map

    

def render_custom_views(render_path, views, gaussians, pipeline, background, train_test_exp, separate_sh, pruned_gs_las):
    makedirs(render_path, exist_ok=True)
    for idx, (view_left, view_right, baseline) in enumerate(views):
        result_left = render(view_left, gaussians, pipeline, background, use_trained_exp=train_test_exp, separate_sh=separate_sh)
        rendering_left = result_left["render"]
        rendering_left_np = rendering_left.permute(1, 2, 0).cpu().numpy()
        rendering_left_np = (rendering_left_np * 255).astype(np.uint8)
        torchvision.utils.save_image(rendering_left, os.path.join(render_path, f"{view_left.cam_name}_left.png"))
        inv_depth_left = result_left["depth"].detach().cpu().numpy()[0]
        # save_depth_image(inv_depth_left, os.path.join(render_path,f"{idx}_left_depth.png"))

        result_right = render(view_right, gaussians, pipeline, background, use_trained_exp=train_test_exp, separate_sh=separate_sh)
        rendering_right = result_right["render"]
        rendering_right_np = rendering_right.permute(1, 2, 0).cpu().numpy()
        rendering_right_np = (rendering_right_np * 255).astype(np.uint8)
        torchvision.utils.save_image(rendering_right, os.path.join(render_path, f"{view_left.cam_name}_right.png"))
        inv_depth_right = result_right["depth"].detach().cpu().numpy()[0]
        # save_depth_image(inv_depth_right, os.path.join(render_path,f"{idx}_right_depth.png"))

        
        # depth_map = render_depth_from_GS(pruned_gs_las, views[0])
        # save_depth_image(depth_map, os.path.join(render_path, f"{idx}_left_depth_from_gs.png"))

        # verify_depth_consistency(rendering_left_np, rendering_right_np, inv_depth_left, inv_depth_right, baseline, view_left.f_x, render_path)
        # remaped_left = remap_left_view(rendering_left_np, rendering_right_np, depth_map, baseline, view_left.focal_length)
        # cv2.imwrite(os.path.join(render_path, f"{idx}_left_warped_to_right.png"), remaped_left[...,::-1])
        # generate_pcd(rendering_left_np, inv_depth_left, view_left.f_x, view_left.f_y, view_left.image_width/2, view_left.image_height/2, os.path.join(render_path, f"{idx}_left_pcd.ply"))

        

def generate_pcd(img_rgb, depth, fx,fy,cx,cy, save_path):
    """Generate point cloud from image and depth map.
    
    Args:
        img_bgr: BGR image array
        depth: Depth map array
        fx: Focal length x
        fy: Focal length y
        cx: Center x
        cy: Center y
        save_path: Path to save the point cloud
    """
    # Create point cloud
    h, w = depth.shape
    pcd = o3d.geometry.PointCloud()
    
    # Get valid depth points
    valid_mask = (depth > 0) & (depth < 100)
    y_coords, x_coords = np.where(valid_mask)
    
    # Convert image coords to camera coords
    x = (x_coords - cx) * depth[y_coords, x_coords] / fx
    y = (y_coords - cy) * depth[y_coords, x_coords] / fy
    z = depth[y_coords, x_coords]
    
    
    points = np.stack([x, y, z], axis=1).astype(np.float32)
    colors = img_rgb[y_coords, x_coords].astype(np.float32) / 255.0

    las = laspy.create(point_format=2, file_version="1.2")
    
    # Set coordinates
    las.x = points[:,0]
    las.y = points[:,1] 
    las.z = points[:,2]
    
    # Set colors (convert to uint16)
    las.red = (colors[:,0] * 65535).astype(np.uint16)   # B to R
    las.green = (colors[:,1] * 65535).astype(np.uint16) # G to G  
    las.blue = (colors[:,2] * 65535).astype(np.uint16)  # R to B
    
    # Save LAS file
    las_path = save_path.replace('.ply', '.las')
    las.write(las_path)
        
class Pipeline:
    def __init__(self):
        self.convert_SHs_python = False
        self.compute_cov3D_python = False
        self.debug = False
        self.antialiasing = False
    
def load_intrinsics(data):
    intrinsics = np.array([[data['fx'], 0, data['width']/2], [0, data['fy'], data['height']/2], [0, 0, 1]])
    return intrinsics

if __name__ == "__main__":
    model_path  = 'output/DJI_20241210154557_0004_D_oriParam/point_cloud/iteration_30000/point_cloud.ply'
    cameras_json_file = 'output/DJI_20241210154557_0004_D_oriParam/cameras.json'
    cam_traj_file = '/workspace/3D/gaussian-opacity-fields/output/DJI_20241210154557_0004_D/pseudo/cam_traj.pkl'
    baseline = 0.7
    output_path = f'output/DJI_20241210154557_0004_D_oriParam/customviews3'
    pruned_gs_las = None
    pipeline = Pipeline()
    with torch.no_grad():
        gaussians = GaussianModel(3)
        gaussians.load_ply(model_path, False)

        bg_color = [0, 0, 0]
        background = torch.tensor(bg_color, dtype=torch.float32, device="cuda")

        cameras = construct_camera_list(cameras_json_file, baseline = baseline)
        # with open(cam_traj_file, 'rb') as f:    
        #     cam_traj = pickle.load(f)
            # cam_traj[0][0].translate([0, 2, 0])
            # cam_traj[0][0].rotate([5, 0, 0])
        render_custom_views(output_path, cameras, gaussians, pipeline, background, False, False, pruned_gs_las)
